// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 问卷填写服务
 * @description 问卷响应的提交、查询、统计等操作
 */

/** 提交问卷响应 POST /api/response */
export async function submitResponse(params: API.ISubmitResponseParams) {
  return request<API.ResType<API.ISubmitResponseResult>>('/api/response', {
    method: 'POST',
    data: params,
  });
}

/** 获取响应列表 GET /api/response */
export async function getResponseList(params?: API.IResponseQuery) {
  return request<API.ResType<API.IResponseListResponse>>('/api/response', {
    method: 'GET',
    params,
  });
}

/** 获取响应详情 GET /api/response/:id */
export async function getResponseDetail(id: number) {
  return request<API.ResType<any>>(`/api/response/${id}`, {
    method: 'GET',
  });
}

/** 检查重复提交 GET /api/response/check */
export async function checkDuplicateResponse(
  params: API.ICheckDuplicateParams,
) {
  return request<API.ResType<{ is_duplicate: boolean; response_id?: number }>>(
    '/api/response/check',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取问卷统计 GET /api/response/statistics/:id */
export async function getResponseStatistics(id: number) {
  return request<API.ResType<API.IResponseStatistics>>(
    `/api/response/statistics/${id}`,
    {
      method: 'GET',
    },
  );
}

/** 获取评分信息 GET /api/response/rating-info/:id */
export async function getRatingInfo(id: number) {
  return request<API.ResType<API.IRatingInfo>>(
    `/api/response/rating-info/${id}`,
    {
      method: 'GET',
    },
  );
}
