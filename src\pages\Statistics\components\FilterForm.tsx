import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { <PERSON>ton, Card, Col, Form, Row, Select, Space } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';

const { Option } = Select;

interface FilterFormProps {
  schoolList: API.ISSoSchoolInfo[];
  loading?: boolean;
  onFilter: (values: API.IStatisticsQuery) => void;
  onReset: () => void;
}

/**
 * 数据筛选表单组件
 */
const FilterForm: React.FC<FilterFormProps> = ({
  schoolList,
  loading = false,
  onFilter,
  onReset,
}) => {
  const [form] = Form.useForm();

  // 处理筛选
  const handleFilter = (values: any) => {
    const filterParams: API.IStatisticsQuery = {
      sso_school_code: values.sso_school_code,
      month: values.month ? dayjs(values.month).format('YYYY-MM') : undefined,
    };
    onFilter(filterParams);
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  // 生成月份选项（最近12个月）
  const getMonthOptions = () => {
    const months = [];
    const current = dayjs();

    for (let i = 0; i < 12; i++) {
      const month = current.subtract(i, 'month');
      months.push({
        value: month.format('YYYY-MM'),
        label: month.format('YYYY年MM月'),
      });
    }

    return months;
  };

  // 初始化默认值
  useEffect(() => {
    form.setFieldsValue({
      month: dayjs().format('YYYY-MM'), // 默认当前月份
    });
  }, [form]);

  return (
    <Card style={{ marginBottom: 16 }}>
      <Form
        form={form}
        layout="inline"
        onFinish={handleFilter}
        style={{ width: '100%' }}
      >
        <Row gutter={16} style={{ width: '100%' }}>
          <Col span={6}>
            <Form.Item name="sso_school_code" label="学校">
              <Select
                placeholder="请选择学校"
                allowClear
                showSearch
                filterOption={(input, option) =>
                  (option?.children as unknown as string)
                    ?.toLowerCase()
                    .includes(input.toLowerCase())
                }
                style={{ width: '100%' }}
              >
                {schoolList.map((school) => (
                  <Option key={school.id} value={school.id}>
                    {school.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item name="month" label="月份">
              <Select
                placeholder="请选择月份"
                allowClear
                style={{ width: '100%' }}
              >
                {getMonthOptions().map((month) => (
                  <Option key={month.value} value={month.value}>
                    {month.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                >
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default FilterForm;
