import { useModel } from '@umijs/max';
import React from 'react';
import EvaluationForm from './EvaluationForm';
import PhoneVerify from './PhoneVerify';
import QuestionnaireSelect from './QuestionnaireSelect';
import StudentSelect from './StudentSelect';

/**
 * 家长端主页面
 * @description 根据当前步骤渲染对应的页面组件
 */
const ParentPage: React.FC = () => {
  const { currentStep } = useModel('parent');

  // 根据当前步骤渲染对应组件
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'phone':
        return <PhoneVerify />;
      case 'student':
        return <StudentSelect />;
      case 'questionnaire':
        return <QuestionnaireSelect />;
      case 'evaluation':
        return <EvaluationForm />;
      default:
        return <PhoneVerify />;
    }
  };

  return <div className="parent-page">{renderCurrentStep()}</div>;
};

export default ParentPage;
