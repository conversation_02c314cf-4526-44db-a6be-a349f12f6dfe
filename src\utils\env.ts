import { parse } from 'querystring';

/**
 * url中的查询字符串转化为json格式
 *
 * @param {string} [queryString] 待转换的查询串
 */
export const getQueryObj = (queryString?: string) =>
  parse(
    queryString || window.location.href.split('?')[1],
  ) as NodeJS.Dict<string>;

/**
 * 获取认证cookie
 *
 * @return {*}
 */
export const getAuthCookie = (): string => {
  const reg = new RegExp('(^| )csrfToken=([^;]*)(;|$)');
  const arr = document.cookie.match(reg);
  if (arr) {
    return decodeURI(arr[2]);
  }
  return '';
};
