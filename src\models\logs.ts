import {
  getOperationLogDetail,
  getOperationLogList,
  getOperationLogStatistics,
} from '@/services';
import { handleApiResponse, handleException } from '@/utils/errorHandler';
import { useCallback, useRef, useState } from 'react';

/**
 * 日志管理数据模型
 */
export default function useLogsModel() {
  // 加载状态
  const [loading, setLoading] = useState(false);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);

  // 日志列表数据
  const [logList, setLogList] = useState<API.IOperationLogDetail[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 日志统计数据
  const [statistics, setStatistics] =
    useState<API.IOperationLogStatistics | null>(null);

  // 日志详情数据
  const [logDetail, setLogDetail] = useState<API.IOperationLogDetail | null>(
    null,
  );

  // 查询条件
  const [queryParams, setQueryParams] = useState<API.IOperationLogQuery>({});

  // 自动刷新定时器
  const autoRefreshTimer = useRef<NodeJS.Timeout | null>(null);

  // 获取日志列表
  const fetchLogList = useCallback(
    async (params?: API.IOperationLogQuery) => {
      setLoading(true);
      try {
        const queryData = {
          ...queryParams,
          ...params,
          page: params?.page || currentPage,
          limit: params?.limit || pageSize,
        };

        const response = await getOperationLogList(queryData);
        const result = handleApiResponse(response);

        if (result.success) {
          setLogList(result.data?.list || []);
          setTotal(result.data?.total || 0);
          if (params?.page) setCurrentPage(params.page);
          if (params?.limit) setPageSize(params.limit);
          return result.data;
        } else {
          setLogList([]);
          setTotal(0);
          return null;
        }
      } catch (error) {
        handleException(error, '获取日志列表失败');
        setLogList([]);
        setTotal(0);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [queryParams, currentPage, pageSize],
  );

  // 获取日志统计
  const fetchStatistics = useCallback(
    async (params?: API.IOperationLogQuery) => {
      setStatisticsLoading(true);
      try {
        const response = await getOperationLogStatistics(params);
        const result = handleApiResponse(response);

        if (result.success) {
          setStatistics(result.data || null);
          return result.data;
        } else {
          setStatistics(null);
          return null;
        }
      } catch (error) {
        handleException(error, '获取日志统计失败');
        setStatistics(null);
        return null;
      } finally {
        setStatisticsLoading(false);
      }
    },
    [],
  );

  // 获取日志详情
  const fetchLogDetail = useCallback(async (id: number) => {
    setDetailLoading(true);
    try {
      const response = await getOperationLogDetail(id);
      const result = handleApiResponse(response);

      if (result.success) {
        setLogDetail(result.data || null);
        return result.data;
      } else {
        setLogDetail(null);
        return null;
      }
    } catch (error) {
      handleException(error, '获取日志详情失败');
      setLogDetail(null);
      return null;
    } finally {
      setDetailLoading(false);
    }
  }, []);

  // 更新查询条件并刷新数据
  const updateQuery = useCallback(
    async (newParams: API.IOperationLogQuery) => {
      setQueryParams(newParams);
      setCurrentPage(1); // 重置到第一页

      // 并行获取列表和统计数据
      await Promise.all([
        fetchLogList({ ...newParams, page: 1 }),
        fetchStatistics(newParams),
      ]);
    },
    [fetchLogList, fetchStatistics],
  );

  // 分页变更
  const handlePageChange = useCallback(
    async (page: number, size?: number) => {
      await fetchLogList({ page, limit: size });
    },
    [fetchLogList],
  );

  // 启动自动刷新
  const startAutoRefresh = useCallback(() => {
    if (autoRefreshTimer.current) {
      clearInterval(autoRefreshTimer.current);
    }

    autoRefreshTimer.current = setInterval(() => {
      fetchLogList();
      fetchStatistics(queryParams);
    }, 5 * 60 * 1000); // 5分钟刷新一次
  }, [fetchLogList, fetchStatistics, queryParams]);

  // 停止自动刷新
  const stopAutoRefresh = useCallback(() => {
    if (autoRefreshTimer.current) {
      clearInterval(autoRefreshTimer.current);
      autoRefreshTimer.current = null;
    }
  }, []);

  // 清空日志详情
  const clearLogDetail = useCallback(() => {
    setLogDetail(null);
  }, []);

  // 重置查询条件
  const resetQuery = useCallback(async () => {
    setQueryParams({});
    setCurrentPage(1);
    await Promise.all([fetchLogList({ page: 1 }), fetchStatistics({})]);
  }, [fetchLogList, fetchStatistics]);

  return {
    // 状态
    loading,
    statisticsLoading,
    detailLoading,
    logList,
    total,
    currentPage,
    pageSize,
    statistics,
    logDetail,
    queryParams,

    // 方法
    fetchLogList,
    fetchStatistics,
    fetchLogDetail,
    updateQuery,
    handlePageChange,
    startAutoRefresh,
    stopAutoRefresh,
    clearLogDetail,
    resetQuery,
  };
}
