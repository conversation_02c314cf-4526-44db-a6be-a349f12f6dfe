.phone-verify-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  .phone-verify-content {
    width: 100%;
    max-width: 480px;

    .verify-card {
      border-radius: 16px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 10%);
      border: none;

      .ant-card-body {
        padding: 40px;
      }
    }

    .card-header {
      text-align: center;
      margin-bottom: 32px;

      .header-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 16px;
        display: block;
      }

      .header-title {
        margin-bottom: 8px !important;
        color: #262626;
      }

      .header-subtitle {
        font-size: 16px;
        color: #8c8c8c;
      }
    }

    .verify-form {
      margin-bottom: 24px;

      .ant-form-item-label > label {
        font-weight: 500;
        color: #262626;
      }

      .ant-input-affix-wrapper {
        border-radius: 8px;
        border: 2px solid #f0f0f0;
        transition: all 0.3s;

        &:hover,
        &:focus,
        &.ant-input-affix-wrapper-focused {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 10%);
        }
      }

      .verify-button {
        height: 48px;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 500;
        margin-top: 16px;
        background: linear-gradient(135deg, #1890ff, #096dd9);
        border: none;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 30%);

        &:hover {
          background: linear-gradient(135deg, #40a9ff, #1890ff);
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(24, 144, 255, 40%);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }

    .verify-tips {
      .ant-alert {
        border-radius: 8px;
        border: 1px solid #e6f7ff;

        .ant-alert-description {
          p {
            margin: 4px 0;
            color: #595959;
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .phone-verify-container {
    padding: 16px;

    .phone-verify-content {
      .verify-card {
        .ant-card-body {
          padding: 24px;
        }
      }

      .card-header {
        margin-bottom: 24px;

        .header-icon {
          font-size: 40px;
        }

        .header-title {
          font-size: 20px;
        }

        .header-subtitle {
          font-size: 14px;
        }
      }
    }
  }
}
