// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/** 登录 POST /auth/login */
export async function login(body: { code: string }) {
  return request<API.ResType<API.User>>('/auth/login', {
    method: 'POST',
    data: body,
  });
}

/** 刷新token POST /auth/refresh-token */
export async function refreshToken(body: { refreshToken: string }) {
  return request<any>('/auth/refresh-token', {
    method: 'POST',
    data: body,
  });
}

/** 获取当前用户信息 GET /auth/current-user */
export async function getCurrentUser() {
  return request<any>('/auth/current-user', {
    method: 'GET',
  });
}

/** 登出 POST /auth/logout */
export async function logout() {
  return request<any>('/auth/logout', {
    method: 'POST',
  });
}

/** 获取学校列表 GET /auth/schools */
export async function getSchoolList() {
  return request<API.ResType<API.ISSoSchoolInfo[]>>('/auth/schools', {
    method: 'GET',
  });
}
