# 问卷管理模块

基于 umi+react 的问卷管理前端页面，实现问卷的创建、编辑、列表展示等功能。

## 功能特性

### 问卷列表页 (`/questionnaire/list`)

- **表格展示**：显示问卷标题、月份、状态、创建时间等信息
- **筛选功能**：支持按月份、状态进行筛选
- **状态管理**：支持问卷状态切换（草稿 → 发布 → 关闭 → 重新发布）
- **操作功能**：编辑、发布、关闭、重新发布、删除问卷
- **分页排序**：集成 Ant Design Table 组件，支持分页和排序
- **权限控制**：用户只能查看和管理自己学校的问卷

### 问卷创建/编辑页 (`/questionnaire/create` | `/questionnaire/edit/:id`)

- **表单验证**：标题、月份为必填项
- **学校自动绑定**：自动使用当前用户所属学校，无需手动选择
- **星级模式**：支持 5 星制和 10 星制选择
- **月份限制**：禁用未来月份选择
- **编辑支持**：同一页面支持创建和编辑功能

## 技术实现

### 数据流设计

使用 umi 的 model 机制管理状态：

```typescript
// 使用问卷模型
const {
  questionnaireList,
  loading,
  total,
  schoolList,
  fetchQuestionnaireList,
  createQuestionnaireAction,
  updateQuestionnaireAction,
  // ...
} = useModel('questionnaire');
```

### 服务接口

所有后端接口调用通过 service 层统一管理：

```typescript
// 问卷管理服务
import {
  getQuestionnaireList,
  createQuestionnaire,
  updateQuestionnaire,
  deleteQuestionnaire,
  // ...
} from '@/services';
```

### 路由配置

```typescript
{
  name: '问卷管理',
  path: '/questionnaire',
  routes: [
    {
      name: '问卷列表',
      path: '/questionnaire/list',
      component: './Questionnaire/List',
    },
    {
      name: '创建问卷',
      path: '/questionnaire/create',
      component: './Questionnaire/Create',
    },
    {
      name: '编辑问卷',
      path: '/questionnaire/edit/:id',
      component: './Questionnaire/Create',
    },
  ],
}
```

## 文件结构

```
src/pages/Questionnaire/
├── List/
│   ├── index.tsx          # 问卷列表页面
│   └── index.less         # 列表页样式
├── Create/
│   ├── index.tsx          # 问卷创建/编辑页面
│   └── index.less         # 创建页样式
└── README.md              # 模块说明文档

src/models/
└── questionnaire.ts       # 问卷数据模型

src/services/
├── questionnaire.ts       # 问卷管理服务
├── sso.ts                # SSO 认证服务
└── typeings.d.ts         # 类型定义
```

## 使用说明

### 1. 访问问卷列表

导航到 `/questionnaire/list` 查看所有问卷，支持：

- 按学校、月份、状态筛选
- 点击"创建问卷"按钮新建问卷
- 对问卷进行编辑、发布、关闭、删除操作

### 2. 创建问卷

点击"创建问卷"按钮，填写：

- 问卷标题（必填）
- 问卷月份（必填，不能选择未来月份）
- 星级模式（5 星制/10 星制）
- 问卷描述（可选）
- 所属学校（自动显示当前用户学校，不可修改）

### 3. 编辑问卷

在问卷列表中点击"编辑"按钮，可修改问卷信息。

### 4. 状态管理

- **草稿**：新创建的问卷，可以编辑和发布
- **发布**：已发布的问卷，可以关闭
- **关闭**：已关闭的问卷，可以重新发布

## 依赖说明

- **Ant Design**：UI 组件库
- **dayjs**：日期处理
- **umi**：React 应用框架
- **TypeScript**：类型支持

## 注意事项

1. 用户只能管理自己学校的问卷，无法查看或操作其他学校的问卷
2. 问卷创建时会自动绑定到当前用户所属的学校
3. 问卷月份不能选择未来月份
4. 删除操作不可恢复，请谨慎操作
5. 所有表单都有完整的验证机制
