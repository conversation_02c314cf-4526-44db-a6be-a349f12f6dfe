// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 问卷管理服务
 * @description 问卷的创建、查询、更新、删除等操作
 */

/** 创建问卷 POST /api/questionnaire */
export async function createQuestionnaire(
  params: API.ICreateQuestionnaireParams,
) {
  return request<API.ResType<API.IQuestionnaireDetail>>('/api/questionnaire', {
    method: 'POST',
    data: params,
  });
}

/** 获取问卷列表 GET /api/questionnaire */
export async function getQuestionnaireList(params?: API.IQuestionnaireQuery) {
  return request<API.ResType<API.IQuestionnaireListResponse>>(
    '/api/questionnaire',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取问卷详情 GET /api/questionnaire/:id */
export async function getQuestionnaireDetail(id: number) {
  return request<API.ResType<API.IQuestionnaireDetail>>(
    `/api/questionnaire/${id}`,
    {
      method: 'GET',
    },
  );
}

/** 更新问卷 PUT /api/questionnaire/:id */
export async function updateQuestionnaire(
  id: number,
  params: API.ICreateQuestionnaireParams,
) {
  return request<API.ResType<API.IQuestionnaireDetail>>(
    `/api/questionnaire/${id}`,
    {
      method: 'PUT',
      data: params,
    },
  );
}

/** 更新问卷状态 PUT /api/questionnaire/:id/status */
export async function updateQuestionnaireStatus(
  id: number,
  params: API.IUpdateQuestionnaireStatusParams,
) {
  return request<API.ResType<API.IQuestionnaireDetail>>(
    `/api/questionnaire/${id}/status`,
    {
      method: 'PUT',
      data: params,
    },
  );
}

/** 删除问卷 DELETE /api/questionnaire/:id */
export async function deleteQuestionnaire(
  id: number,
  options?: { skipGlobalErrorHandler?: boolean },
) {
  return request<API.ResType<boolean>>(`/api/questionnaire/${id}`, {
    method: 'DELETE',
    skipGlobalErrorHandler: options?.skipGlobalErrorHandler,
  });
}
