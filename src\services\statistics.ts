// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 统计服务
 * @description 学校统计、教师统计、趋势分析等操作
 */

/** 学校统计概览 GET /api/statistics/school */
export async function getSchoolStatistics(params?: API.IStatisticsQuery) {
  return request<API.ResType<API.ISchoolStatistics>>('/api/statistics/school', {
    method: 'GET',
    params,
  });
}

/** 学校响应趋势 GET /api/statistics/school/:schoolCode/trend */
export async function getSchoolResponseTrend(
  schoolCode: string,
  params?: API.IStatisticsQuery,
) {
  return request<API.ResType<API.ITrendData[]>>(
    `/api/statistics/school/${schoolCode}/trend`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 教师排名 GET /api/statistics/teacher-ranking */
export async function getTeacherRanking(params?: API.IStatisticsQuery) {
  return request<API.ResType<API.ITeacherRankingResponse>>(
    '/api/statistics/teacher-ranking',
    {
      method: 'GET',
      params,
    },
  );
}

/** 趋势分析 GET /api/statistics/trend */
export async function getTrendAnalysis(params?: API.IStatisticsQuery) {
  return request<API.ResType<API.ITrendData[]>>('/api/statistics/trend', {
    method: 'GET',
    params,
  });
}

/** 教师统计概览 GET /api/statistics/teacher */
export async function getTeacherStatistics(params?: API.IStatisticsQuery) {
  return request<API.ResType<API.ITeacherStatistics>>(
    '/api/statistics/teacher',
    {
      method: 'GET',
      params,
    },
  );
}

/** 教师评分分布 GET /api/statistics/teacher/:teacherId/distribution */
export async function getTeacherScoreDistribution(
  teacherId: string,
  params?: API.IStatisticsQuery,
) {
  return request<API.ResType<API.IScoreDistribution[]>>(
    `/api/statistics/teacher/${teacherId}/distribution`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 教师关键词云 GET /api/statistics/teacher/:teacherId/keywords */
export async function getTeacherKeywords(
  teacherId: string,
  params?: API.IStatisticsQuery,
) {
  return request<API.ResType<API.IKeywordData[]>>(
    `/api/statistics/teacher/${teacherId}/keywords`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 教师评价趋势 GET /api/statistics/teacher/:teacherId/trend */
export async function getTeacherEvaluationTrend(
  teacherId: string,
  params?: API.IStatisticsQuery,
) {
  return request<API.ResType<API.ITrendData[]>>(
    `/api/statistics/teacher/${teacherId}/trend`,
    {
      method: 'GET',
      params,
    },
  );
}
