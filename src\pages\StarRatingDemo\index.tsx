import { StarRating } from '@/components';
import { Button, Card, Col, Row, Space, Switch, Typography } from 'antd';
import React, { useState } from 'react';
import './index.less';

const { Title, Text, Paragraph } = Typography;

/**
 * 星级评分组件演示页面
 */
const StarRatingDemo: React.FC = () => {
  const [value5Star, setValue5Star] = useState(60); // 60% = 3星
  const [value10Star, setValue10Star] = useState(70); // 70% = 7星
  const [readOnly, setReadOnly] = useState(false);

  const resetValues = () => {
    setValue5Star(60);
    setValue10Star(70);
  };

  return (
    <div className="star-rating-demo">
      <div className="demo-container">
        <Title level={2}>星级评分组件演示</Title>
        <Paragraph>
          可复用的星级评分React组件，支持5星制和10星制，使用百分制评分（0-100）。
        </Paragraph>

        {/* 控制面板 */}
        <Card title="控制面板" style={{ marginBottom: 24 }}>
          <Space size="large">
            <div>
              <Text strong>只读模式：</Text>
              <Switch checked={readOnly} onChange={setReadOnly} />
            </div>
            <Button onClick={resetValues}>重置评分</Button>
          </Space>
        </Card>

        {/* 基础用法 */}
        <Card title="基础用法" style={{ marginBottom: 24 }}>
          <Row gutter={[24, 24]}>
            <Col span={12}>
              <div className="demo-item">
                <Title level={4}>5星制评分</Title>
                <StarRating
                  mode={5}
                  value={value5Star}
                  onChange={setValue5Star}
                  readOnly={readOnly}
                />
                <Text type="secondary">当前值: {value5Star}%</Text>
              </div>
            </Col>
            <Col span={12}>
              <div className="demo-item">
                <Title level={4}>10星制评分</Title>
                <StarRating
                  mode={10}
                  value={value10Star}
                  onChange={setValue10Star}
                  readOnly={readOnly}
                />
                <Text type="secondary">当前值: {value10Star}%</Text>
              </div>
            </Col>
          </Row>
        </Card>

        {/* 不同尺寸 */}
        <Card title="不同尺寸" style={{ marginBottom: 24 }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div className="demo-item">
              <Title level={5}>小尺寸 (small)</Title>
              <StarRating mode={5} value={80} readOnly={true} size="small" />
            </div>
            <div className="demo-item">
              <Title level={5}>默认尺寸 (default)</Title>
              <StarRating mode={5} value={80} readOnly={true} size="default" />
            </div>
            <div className="demo-item">
              <Title level={5}>大尺寸 (large)</Title>
              <StarRating mode={5} value={80} readOnly={true} size="large" />
            </div>
          </Space>
        </Card>

        {/* 主题变体 */}
        <Card title="主题变体" style={{ marginBottom: 24 }}>
          <Row gutter={[24, 24]}>
            <Col span={12}>
              <div className="demo-item">
                <Title level={5}>默认主题</Title>
                <StarRating mode={5} value={80} readOnly={true} />
              </div>
            </Col>
            <Col span={12}>
              <div className="demo-item">
                <Title level={5}>蓝色主题</Title>
                <StarRating
                  mode={5}
                  value={80}
                  readOnly={true}
                  className="star-rating-theme-blue"
                />
              </div>
            </Col>
            <Col span={12}>
              <div className="demo-item">
                <Title level={5}>绿色主题</Title>
                <StarRating
                  mode={5}
                  value={80}
                  readOnly={true}
                  className="star-rating-theme-green"
                />
              </div>
            </Col>
            <Col span={12}>
              <div className="demo-item">
                <Title level={5}>红色主题</Title>
                <StarRating
                  mode={5}
                  value={80}
                  readOnly={true}
                  className="star-rating-theme-red"
                />
              </div>
            </Col>
          </Row>
        </Card>

        {/* 显示选项 */}
        <Card title="显示选项" style={{ marginBottom: 24 }}>
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <div className="demo-item">
              <Title level={5}>显示评分值</Title>
              <StarRating
                mode={5}
                value={75}
                readOnly={true}
                showValue={true}
              />
            </div>
            <div className="demo-item">
              <Title level={5}>隐藏评分值</Title>
              <StarRating
                mode={5}
                value={75}
                readOnly={true}
                showValue={false}
              />
            </div>
          </Space>
        </Card>

        {/* 使用示例 */}
        <Card title="代码示例">
          <Paragraph>
            <Text code>
              {`// 基础用法
<StarRating
  mode={5}
  value={80}
  onChange={(value) => console.log(value)}
/>

// 只读模式
<StarRating
  mode={10}
  value={70}
  readOnly={true}
  size="large"
/>

// 自定义主题
<StarRating
  mode={5}
  value={90}
  className="star-rating-theme-blue"
/>`}
            </Text>
          </Paragraph>
        </Card>

        {/* 属性说明 */}
        <Card title="组件属性">
          <div className="props-table">
            <table>
              <thead>
                <tr>
                  <th>属性</th>
                  <th>类型</th>
                  <th>默认值</th>
                  <th>说明</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>value</td>
                  <td>number</td>
                  <td>0</td>
                  <td>当前评分值（0-100）</td>
                </tr>
                <tr>
                  <td>mode</td>
                  <td>5 | 10</td>
                  <td>5</td>
                  <td>星级模式（5或10星）</td>
                </tr>
                <tr>
                  <td>onChange</td>
                  <td>(value: number) =&gt; void</td>
                  <td>-</td>
                  <td>评分变化回调函数</td>
                </tr>
                <tr>
                  <td>readOnly</td>
                  <td>boolean</td>
                  <td>false</td>
                  <td>是否只读模式</td>
                </tr>
                <tr>
                  <td>size</td>
                  <td>
                    &apos;small&apos; | &apos;default&apos; | &apos;large&apos;
                  </td>
                  <td>&apos;default&apos;</td>
                  <td>星星大小</td>
                </tr>
                <tr>
                  <td>showValue</td>
                  <td>boolean</td>
                  <td>true</td>
                  <td>是否显示评分值</td>
                </tr>
                <tr>
                  <td>className</td>
                  <td>string</td>
                  <td>-</td>
                  <td>自定义类名</td>
                </tr>
              </tbody>
            </table>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default StarRatingDemo;
