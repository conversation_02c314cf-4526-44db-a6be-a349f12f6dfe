import { StarFilled, StarOutlined } from '@ant-design/icons';
import React, { useCallback, useState } from 'react';
import './index.less';

export interface StarRatingProps {
  /** 当前评分值（0-100） */
  value: number;
  /** 星级模式（5 或 10，控制显示星数） */
  mode: 5 | 10;
  /** 评分变化回调函数 */
  onChange?: (value: number) => void;
  /** 是否只读模式 */
  readOnly?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 星星大小 */
  size?: 'small' | 'default' | 'large';
  /** 是否显示评分值 */
  showValue?: boolean;
}

/**
 * 星级评分组件
 * @description 可复用的星级评分组件，支持5星制和10星制
 */
const StarRating: React.FC<StarRatingProps> = ({
  value = 0,
  mode = 5,
  onChange,
  readOnly = false,
  className = '',
  size = 'default',
  showValue = true,
}) => {
  const [hoverValue, setHoverValue] = useState<number | null>(null);

  // 计算每星对应的分值
  const getStarValue = useCallback(
    (starIndex: number) => {
      const valuePerStar = 100 / mode;
      return (starIndex + 1) * valuePerStar;
    },
    [mode],
  );

  // 计算当前应该显示的评分值
  const getCurrentValue = useCallback(() => {
    return hoverValue !== null ? hoverValue : value;
  }, [hoverValue, value]);

  // 计算星星的填充状态
  const getStarFillStatus = useCallback(
    (starIndex: number) => {
      const currentValue = getCurrentValue();
      const starValue = getStarValue(starIndex);
      const prevStarValue = starIndex === 0 ? 0 : getStarValue(starIndex - 1);

      if (currentValue >= starValue) {
        return 'filled'; // 完全填充
      } else if (currentValue > prevStarValue) {
        return 'partial'; // 部分填充
      } else {
        return 'empty'; // 空心
      }
    },
    [getCurrentValue, getStarValue],
  );

  // 处理星星点击
  const handleStarClick = useCallback(
    (starIndex: number) => {
      if (readOnly || !onChange) return;

      const newValue = getStarValue(starIndex);
      onChange(newValue);
    },
    [readOnly, onChange, getStarValue],
  );

  // 处理鼠标悬停
  const handleStarHover = useCallback(
    (starIndex: number) => {
      if (readOnly) return;

      const hoverVal = getStarValue(starIndex);
      setHoverValue(hoverVal);
    },
    [readOnly, getStarValue],
  );

  // 处理鼠标离开
  const handleMouseLeave = useCallback(() => {
    if (readOnly) return;
    setHoverValue(null);
  }, [readOnly]);

  // 渲染单个星星
  const renderStar = useCallback(
    (starIndex: number) => {
      const fillStatus = getStarFillStatus(starIndex);
      const isFilled = fillStatus === 'filled' || fillStatus === 'partial';

      return (
        <span
          key={starIndex}
          className={`star-rating-star ${fillStatus} ${
            readOnly ? 'readonly' : 'interactive'
          }`}
          onClick={() => handleStarClick(starIndex)}
          onMouseEnter={() => handleStarHover(starIndex)}
          role={readOnly ? 'img' : 'button'}
          aria-label={`${starIndex + 1} 星`}
          tabIndex={readOnly ? -1 : 0}
          onKeyDown={(e) => {
            if (!readOnly && (e.key === 'Enter' || e.key === ' ')) {
              e.preventDefault();
              handleStarClick(starIndex);
            }
          }}
        >
          {isFilled ? (
            <StarFilled className="star-icon filled" />
          ) : (
            <StarOutlined className="star-icon empty" />
          )}
        </span>
      );
    },
    [getStarFillStatus, readOnly, handleStarClick, handleStarHover],
  );

  // 计算显示的评分值
  const displayValue = getCurrentValue();
  const displayScore = (displayValue / 100) * mode;

  return (
    <div
      className={`star-rating ${className} star-rating-${size} ${
        readOnly ? 'readonly' : ''
      }`}
      onMouseLeave={handleMouseLeave}
    >
      <div className="star-rating-stars">
        {Array.from({ length: mode }, (_, index) => renderStar(index))}
      </div>

      {showValue && (
        <div className="star-rating-value">
          <span className="score">{displayScore.toFixed(1)}</span>
          <span className="total">/{mode}</span>
        </div>
      )}
    </div>
  );
};

export default StarRating;
