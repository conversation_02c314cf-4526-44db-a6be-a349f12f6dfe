.questionnaire-list {
  .search-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
  }

  .action-bar {
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .status-tag {
    &.draft {
      color: #666;
      background-color: #f5f5f5;
      border-color: #d9d9d9;
    }

    &.published {
      color: #52c41a;
      background-color: #f6ffed;
      border-color: #b7eb8f;
    }

    &.closed {
      color: #ff4d4f;
      background-color: #fff2f0;
      border-color: #ffccc7;
    }
  }

  .table-actions {
    .ant-btn {
      padding: 0;
      height: auto;
      line-height: 1;
    }
  }

  // 移动端标题单元格样式
  .mobile-title-cell {
    .title {
      font-weight: 500;
      margin-bottom: 4px;
      line-height: 1.4;
    }

    .meta-info {
      display: flex;
      gap: 12px;
      font-size: 12px;
      color: #666;

      .month {
        color: #1890ff;
      }

      .time {
        color: #999;
      }
    }
  }

  // 响应式样式
  @media (max-width: 768px) {
    // 移动端整体布局调整
    .ant-pro-table {
      .ant-pro-table-search {
        padding: 12px 16px;
      }

      .ant-pro-table-list-toolbar {
        padding: 8px 16px;
        flex-wrap: wrap;
        gap: 8px;

        .ant-pro-table-list-toolbar-title {
          flex: none;
          margin-bottom: 8px;
        }

        .ant-pro-table-list-toolbar-right {
          flex: 1;
          justify-content: flex-end;
        }
      }
    }

    // 表格样式调整
    .ant-table {
      .ant-table-thead > tr > th {
        padding: 8px 4px;
        font-size: 12px;
      }

      .ant-table-tbody > tr > td {
        padding: 8px 4px;
        font-size: 12px;
      }

      // 状态标签在移动端的样式
      .ant-tag {
        font-size: 10px;
        padding: 0 4px;
        line-height: 16px;
      }
    }

    // 操作按钮样式
    .table-actions {
      .ant-btn {
        font-size: 12px;
        padding: 0 4px;
      }
    }

    // 分页器样式
    .ant-pagination {
      margin: 16px 0 0;
      text-align: center;

      .ant-pagination-simple-pager {
        .ant-pagination-simple-pager-input {
          width: 40px;
        }
      }
    }
  }

  // 平板端样式
  @media (min-width: 769px) and (max-width: 1024px) {
    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 12px 8px;
      }
    }

    .table-actions {
      .ant-btn {
        font-size: 13px;
      }
    }
  }

  // 大屏幕样式优化
  @media (min-width: 1200px) {
    .ant-pro-table-list-toolbar {
      padding: 16px 24px;
    }

    .ant-table {
      .ant-table-thead > tr > th,
      .ant-table-tbody > tr > td {
        padding: 16px 12px;
      }
    }
  }
}
