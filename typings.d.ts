import '@umijs/max/typings';
declare global {
  /** 本地存储前缀 */
  const localStorage_prefix: string;

  /** 综合基础路由与 umi 扩展路由 */
  type IBestAFSRoute = {
    path?: string;
    component?: string;
    name?: string;
    layout?: false;
    redirect?: string;
    keepQuery?: boolean;
    routes?: IBestAFSRoute[];
    wrappers?: Array<string>;
    /** 菜单图标 */
    icon?: string;
    /** 新页面打开 */
    target?: '_blank';
    /** 不展示顶栏 */
    headerRender?: boolean;
    /** 不展示页脚 */
    footerRender?: boolean;
    /** 不展示菜单 */
    menuRender?: boolean;
    /** 不展示菜单顶栏 */
    menuHeaderRender?: boolean;
    /** 权限配置，需要与 plugin-access 插件配合使用 */
    access?: string;
    /** 隐藏子菜单 */
    hideChildrenInMenu?: boolean;
    /** 隐藏自己和子菜单 */
    hideInMenu?: boolean;
    /** 在面包屑中隐藏 */
    hideInBreadcrumb?: boolean;
    /** 子项往上提，仍旧展示 */
    flatMenu?: boolean;
  };
  type InitialState = API.User | undefined;
}
