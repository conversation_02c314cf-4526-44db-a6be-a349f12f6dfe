import { Column } from '@ant-design/charts';
import { Card, Empty, Spin } from 'antd';
import React from 'react';

interface SubjectChartProps {
  data: API.ITeacherRanking[];
  loading?: boolean;
  title?: string;
}

/**
 * 学科平均分对比柱状图组件
 */
const SubjectChart: React.FC<SubjectChartProps> = ({
  data,
  loading = false,
  title = '学科平均分对比',
}) => {
  // 处理图表数据 - 按学科分组计算平均分
  const chartData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    const subjectMap = new Map<string, { total: number; count: number }>();

    data.forEach((teacher) => {
      const subject = teacher.sso_teacher_subject || '未知学科';
      const score = teacher.average_score || 0;

      if (subjectMap.has(subject)) {
        const existing = subjectMap.get(subject)!;
        existing.total += score;
        existing.count += 1;
      } else {
        subjectMap.set(subject, { total: score, count: 1 });
      }
    });

    const result = Array.from(subjectMap.entries()).map(
      ([subject, { total, count }]) => ({
        subject,
        averageScore: Number((total / count).toFixed(1)),
        teacherCount: count,
      }),
    );

    // 按平均分降序排列
    return result.sort((a, b) => b.averageScore - a.averageScore);
  }, [data]);

  // 图表配置
  const config = {
    data: chartData,
    xField: 'subject',
    yField: 'averageScore',
    columnWidthRatio: 0.6,
    color: '#1890ff',
    animation: {
      appear: {
        animation: 'grow-in-y',
        duration: 1000,
      },
    },
    tooltip: {
      formatter: (datum: any) => {
        return [
          {
            name: '平均分',
            value: `${datum.averageScore}分`,
          },
          {
            name: '教师数量',
            value: `${datum.teacherCount}人`,
          },
        ];
      },
    },
    yAxis: {
      title: {
        text: '平均分',
      },
      min: 0,
      max: 100,
    },
    xAxis: {
      title: {
        text: '学科',
      },
      label: {
        autoRotate: true,
        autoHide: true,
      },
    },
    meta: {
      averageScore: {
        alias: '平均分',
      },
      subject: {
        alias: '学科',
      },
    },
    label: {
      position: 'top' as const,
      style: {
        fill: '#000',
        opacity: 0.6,
      },
      formatter: (datum: any) => `${datum.averageScore}`,
    },
  };

  return (
    <Card title={title} style={{ marginBottom: 16 }}>
      <Spin spinning={loading}>
        {chartData.length === 0 ? (
          <Empty description="暂无学科数据" style={{ padding: '40px 0' }} />
        ) : (
          <div style={{ height: 400 }}>
            <Column {...config} />
          </div>
        )}
      </Spin>
    </Card>
  );
};

export default SubjectChart;
