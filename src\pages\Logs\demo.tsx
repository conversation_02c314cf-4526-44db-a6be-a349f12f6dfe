import { Card } from 'antd';
import React from 'react';
import LogQueryForm from './components/LogQueryForm';
import LogStatistics from './components/LogStatistics';
import LogTable from './components/LogTable';

/**
 * 日志管理页面演示组件
 * 用于展示各个组件的功能和样式
 */
const LogsDemo: React.FC = () => {
  // 模拟统计数据
  const mockStatistics: API.IOperationLogStatistics = {
    total_operations: 1250,
    success_operations: 1190,
    failed_operations: 60,
    success_rate: 95.2,
    avg_response_time: 120,
    operation_trend: [
      { date: '2024-01-15', count: 980 },
      { date: '2024-01-16', count: 1120 },
      { date: '2024-01-17', count: 1350 },
      { date: '2024-01-18', count: 1180 },
      { date: '2024-01-19', count: 1420 },
      { date: '2024-01-20', count: 1280 },
      { date: '2024-01-21', count: 1250 },
    ],
    module_distribution: [
      { module: '问卷管理', count: 450 },
      { module: '评价提交', count: 380 },
      { module: '统计查询', count: 280 },
      { module: '用户认证', count: 140 },
    ],
    user_activity: [
      { user_id: 'user_001', count: 45 },
      { user_id: 'user_002', count: 38 },
      { user_id: 'user_003', count: 32 },
    ],
  };

  // 模拟日志列表
  const mockLogList: API.IOperationLogDetail[] = [
    {
      id: 1,
      module: 'questionnaire',
      operation_type: 'questionnaire_create',
      description: '创建问卷：2024年1月教师评价问卷',
      user_id: 'user_001',
      user_name: '张管理员',
      school_code: 'school_001',
      level: 'info',
      ip_address: '*************',
      response_time: 120,
      result: 'success',
      created_at: new Date('2024-01-21T10:30:00'),
    },
    {
      id: 2,
      module: 'response',
      operation_type: 'response_submit',
      description: '家长提交教师评价：学生ID student_123',
      user_id: 'parent_001',
      user_name: '李家长',
      school_code: 'school_001',
      level: 'info',
      ip_address: '*************',
      response_time: 85,
      result: 'success',
      created_at: new Date('2024-01-21T10:25:00'),
    },
    {
      id: 3,
      module: 'statistics',
      operation_type: 'statistics_query',
      description: '查询学校统计数据失败：权限不足',
      user_id: 'user_002',
      user_name: '王老师',
      school_code: 'school_002',
      level: 'error',
      ip_address: '*************',
      response_time: 45,
      result: 'error',
      error_stack:
        'Error: Access denied\n    at checkPermission (auth.js:45)\n    at getStatistics (statistics.js:120)',
      created_at: new Date('2024-01-21T10:20:00'),
    },
    {
      id: 4,
      module: 'auth',
      operation_type: 'user_login',
      description: '用户登录系统',
      user_id: 'user_003',
      user_name: '赵校长',
      school_code: 'school_003',
      level: 'info',
      ip_address: '*************',
      response_time: 200,
      result: 'success',
      created_at: new Date('2024-01-21T10:15:00'),
    },
    {
      id: 5,
      module: 'questionnaire',
      operation_type: 'questionnaire_update',
      description: '更新问卷状态：发布问卷',
      user_id: 'user_001',
      user_name: '张管理员',
      school_code: 'school_001',
      level: 'warn',
      ip_address: '*************',
      response_time: 150,
      result: 'success',
      created_at: new Date('2024-01-21T10:10:00'),
    },
  ];

  const handleQuery = (params: API.IOperationLogQuery) => {
    console.log('查询参数:', params);
  };

  const handleReset = () => {
    console.log('重置查询');
  };

  const handlePageChange = (page: number, size?: number) => {
    console.log('分页变更:', page, size);
  };

  const handleRowClick = (record: API.IOperationLogDetail) => {
    console.log('点击日志行:', record);
  };

  return (
    <div style={{ padding: '24px', background: '#f5f5f5' }}>
      <Card title="日志管理页面演示" style={{ marginBottom: 16 }}>
        <p>这是日志管理页面的演示版本，展示了各个组件的功能和样式。</p>
      </Card>

      {/* 查询表单 */}
      <LogQueryForm
        loading={false}
        onQuery={handleQuery}
        onReset={handleReset}
      />

      {/* 统计图表 */}
      <LogStatistics data={mockStatistics} loading={false} />

      {/* 日志列表 */}
      <LogTable
        data={mockLogList}
        total={mockLogList.length}
        currentPage={1}
        pageSize={10}
        loading={false}
        onPageChange={handlePageChange}
        onRowClick={handleRowClick}
      />
    </div>
  );
};

export default LogsDemo;
