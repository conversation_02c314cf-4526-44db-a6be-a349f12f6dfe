import { ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import {
  <PERSON><PERSON>,
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  Row,
  Select,
  Space,
} from 'antd';
import dayjs from 'dayjs';
import React from 'react';

const { Option } = Select;
const { RangePicker } = DatePicker;

interface LogQueryFormProps {
  loading?: boolean;
  onQuery: (values: API.IOperationLogQuery) => void;
  onReset: () => void;
}

/**
 * 日志查询表单组件
 */
const LogQueryForm: React.FC<LogQueryFormProps> = ({
  loading = false,
  onQuery,
  onReset,
}) => {
  const [form] = Form.useForm();

  // 处理查询
  const handleQuery = (values: any) => {
    const queryParams: API.IOperationLogQuery = {
      operation_type: values.operation_type,
      level: values.level,
      keyword: values.keyword?.trim(),
    };

    // 处理日期范围
    if (values.dateRange && values.dateRange.length === 2) {
      queryParams.start_time = values.dateRange[0].startOf('day').toISOString();
      queryParams.end_time = values.dateRange[1].endOf('day').toISOString();
    }

    onQuery(queryParams);
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    onReset();
  };

  // 快捷日期选项
  const dateRangePresets = [
    {
      label: '今天',
      value: () =>
        [dayjs().startOf('day'), dayjs().endOf('day')] as [
          dayjs.Dayjs,
          dayjs.Dayjs,
        ],
    },
    {
      label: '近7天',
      value: () =>
        [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')] as [
          dayjs.Dayjs,
          dayjs.Dayjs,
        ],
    },
    {
      label: '本月',
      value: () =>
        [dayjs().startOf('month'), dayjs().endOf('month')] as [
          dayjs.Dayjs,
          dayjs.Dayjs,
        ],
    },
    {
      label: '上月',
      value: () =>
        [
          dayjs().subtract(1, 'month').startOf('month'),
          dayjs().subtract(1, 'month').endOf('month'),
        ] as [dayjs.Dayjs, dayjs.Dayjs],
    },
  ];

  return (
    <Card style={{ marginBottom: 16 }}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleQuery}
        initialValues={{
          dateRange: [
            dayjs().subtract(6, 'day').startOf('day'),
            dayjs().endOf('day'),
          ],
        }}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item name="dateRange" label="日期范围">
              <RangePicker
                style={{ width: '100%' }}
                presets={dateRangePresets}
                format="YYYY-MM-DD"
                placeholder={['开始日期', '结束日期']}
              />
            </Form.Item>
          </Col>

          <Col span={4}>
            <Form.Item name="level" label="日志级别">
              <Select placeholder="请选择级别" allowClear>
                <Option value="info">信息</Option>
                <Option value="warn">警告</Option>
                <Option value="error">错误</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={4}>
            <Form.Item name="operation_type" label="操作类型">
              <Select placeholder="请选择类型" allowClear>
                <Option value="questionnaire_create">问卷创建</Option>
                <Option value="questionnaire_update">问卷编辑</Option>
                <Option value="questionnaire_delete">问卷删除</Option>
                <Option value="response_submit">评价提交</Option>
                <Option value="statistics_query">统计查询</Option>
                <Option value="user_login">用户登录</Option>
                <Option value="user_logout">用户登出</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={4}>
            <Form.Item name="keyword" label="关键词搜索">
              <Input placeholder="搜索日志内容" allowClear maxLength={50} />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={24}>
            <Form.Item style={{ marginBottom: 0 }}>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SearchOutlined />}
                  loading={loading}
                >
                  查询
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

export default LogQueryForm;
