# StarRating 星级评分组件

可复用的星级评分 React 组件，支持 5 星制和 10 星制，使用百分制评分系统（0-100）。

## 功能特性

### ⭐ 核心功能

- **双模式支持**：支持 5 星制和 10 星制评分
- **百分制评分**：内部使用 0-100 的百分制评分系统
- **交互式评分**：鼠标悬停预览，点击确认评分
- **只读模式**：支持只读展示模式
- **键盘支持**：支持键盘导航和操作

### 🎨 视觉设计

- **Font Awesome 图标**：使用 StarFilled 和 StarOutlined 图标
- **评分值显示**：星星右侧显示评分值，保留 1 位小数
- **自定义主题**：支持通过 CSS 变量自定义主题色
- **多种尺寸**：small、default、large 三种尺寸
- **动画效果**：悬停动画和星星发光效果

### 🔧 技术实现

- **TypeScript**：完整的类型定义
- **响应式设计**：适配移动端和桌面端
- **无障碍支持**：ARIA 标签和键盘导航
- **性能优化**：使用 useCallback 优化渲染

## 组件属性

| 属性 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| value | number | 0 | 当前评分值（0-100） |
| mode | 5 \| 10 | 5 | 星级模式（5 或 10 星） |
| onChange | (value: number) => void | - | 评分变化回调函数 |
| readOnly | boolean | false | 是否只读模式 |
| className | string | - | 自定义类名 |
| size | 'small' \| 'default' \| 'large' | 'default' | 星星大小 |
| showValue | boolean | true | 是否显示评分值 |

## 评分计算规则

### 5 星制模式

- 每星对应 20 分（100/5 = 20）
- 1 星 = 20 分，2 星 = 40 分，3 星 = 60 分，4 星 = 80 分，5 星 = 100 分
- 显示值：(value/100) \* 5，保留 1 位小数

### 10 星制模式

- 每星对应 10 分（100/10 = 10）
- 1 星 = 10 分，2 星 = 20 分，...，10 星 = 100 分
- 显示值：(value/100) \* 10，保留 1 位小数

## 使用示例

### 基础用法

```tsx
import { StarRating } from '@/components';

// 5星制评分
<StarRating
  mode={5}
  value={60}  // 60% = 3星
  onChange={(value) => console.log('评分:', value)}
/>

// 10星制评分
<StarRating
  mode={10}
  value={70}  // 70% = 7星
  onChange={(value) => console.log('评分:', value)}
/>
```

### 只读模式

```tsx
<StarRating mode={5} value={80} readOnly={true} size="large" />
```

### 自定义主题

```tsx
// 蓝色主题
<StarRating
  mode={5}
  value={90}
  className="star-rating-theme-blue"
/>

// 绿色主题
<StarRating
  mode={5}
  value={90}
  className="star-rating-theme-green"
/>
```

### 不同尺寸

```tsx
// 小尺寸
<StarRating mode={5} value={80} size="small" />

// 默认尺寸
<StarRating mode={5} value={80} size="default" />

// 大尺寸
<StarRating mode={5} value={80} size="large" />
```

### 隐藏评分值

```tsx
<StarRating mode={5} value={75} showValue={false} />
```

## 主题定制

组件支持通过 CSS 变量自定义主题：

```css
:root {
  --star-rating-primary-color: #faad14; /* 主色调 */
  --star-rating-secondary-color: #d9d9d9; /* 次要色调 */
  --star-rating-hover-color: #ffc53d; /* 悬停色调 */
  --star-rating-text-color: #262626; /* 文字颜色 */
  --star-rating-text-secondary: #8c8c8c; /* 次要文字颜色 */
}
```

### 预设主题

```css
/* 蓝色主题 */
.star-rating-theme-blue {
  --star-rating-primary-color: #1890ff;
  --star-rating-hover-color: #40a9ff;
}

/* 绿色主题 */
.star-rating-theme-green {
  --star-rating-primary-color: #52c41a;
  --star-rating-hover-color: #73d13d;
}

/* 红色主题 */
.star-rating-theme-red {
  --star-rating-primary-color: #ff4d4f;
  --star-rating-hover-color: #ff7875;
}

/* 紫色主题 */
.star-rating-theme-purple {
  --star-rating-primary-color: #722ed1;
  --star-rating-hover-color: #9254de;
}
```

## 无障碍支持

- **ARIA 标签**：每个星星都有适当的 aria-label
- **键盘导航**：支持 Tab 键导航和 Enter/Space 键操作
- **高对比度**：支持高对比度模式
- **减少动画**：支持 prefers-reduced-motion 设置

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 演示页面

访问 `/star-rating-demo` 查看完整的组件演示和使用示例。

## 注意事项

1. **评分范围**：组件内部使用 0-100 的百分制，外部显示根据 mode 转换
2. **回调函数**：onChange 回调返回的是百分制值（0-100）
3. **性能考虑**：大量星级组件时建议使用 React.memo 优化
4. **主题一致性**：建议在应用中统一使用相同的主题配置
