import { Alert, Card, Space, Typography } from 'antd';
import React from 'react';
import DevTools from './index';

const { Title, Paragraph, Text } = Typography;

/**
 * DevTools 组件演示
 * @description 展示开发工具组件的使用方式和效果
 */
const DevToolsDemo: React.FC = () => {
  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '800px', margin: '0 auto' }}>
        <Title level={2}>DevTools 开发工具组件演示</Title>

        <Alert
          message="环境提示"
          description={
            process.env.NODE_ENV === 'development'
              ? '当前为开发环境，DevTools组件将正常显示'
              : '当前为生产环境，DevTools组件将自动隐藏'
          }
          type={process.env.NODE_ENV === 'development' ? 'success' : 'warning'}
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Card title="组件预览">
            <Paragraph>
              以下是DevTools组件的预览效果，通常显示在侧边栏底部：
            </Paragraph>

            <div
              style={{
                background: '#253650',
                padding: '16px',
                borderRadius: '8px',
                width: '200px',
              }}
            >
              <DevTools />
            </div>
          </Card>

          <Card title="功能说明">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>🔧 环境检测</Text>
                <Paragraph>
                  组件会自动检测当前环境，仅在开发模式下显示。 当前环境：
                  <Text code>{process.env.NODE_ENV}</Text>
                </Paragraph>
              </div>

              <div>
                <Text strong>🎯 快速导航</Text>
                <Paragraph>提供快速访问开发和演示页面的入口，包括：</Paragraph>
                <ul>
                  <li>星级评分演示页面</li>
                  <li>家长端问卷填写流程</li>
                </ul>
              </div>

              <div>
                <Text strong>🎨 视觉设计</Text>
                <Paragraph>
                  现代化的UI设计，包括悬停动画、发光效果和响应式布局。
                  支持侧边栏收起/展开状态的自适应显示。
                </Paragraph>
              </div>
            </Space>
          </Card>

          <Card title="使用方式">
            <Paragraph>
              <Text strong>在布局中使用：</Text>
            </Paragraph>
            <Text code>
              {`import { DevTools } from '@/components';

export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  return {
    // 其他配置...
    menuFooterRender: () => <DevTools />,
  };
};`}
            </Text>
          </Card>
        </Space>
      </div>
    </div>
  );
};

export default DevToolsDemo;
