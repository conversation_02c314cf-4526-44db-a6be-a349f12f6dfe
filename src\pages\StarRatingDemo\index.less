.star-rating-demo {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;

  .demo-container {
    max-width: 1200px;
    margin: 0 auto;

    .demo-item {
      padding: 16px;
      background: #fff;
      border-radius: 8px;
      border: 1px solid #f0f0f0;

      .ant-typography {
        margin-bottom: 12px !important;
      }
    }

    .props-table {
      overflow-x: auto;

      table {
        width: 100%;
        border-collapse: collapse;
        font-size: 14px;

        th,
        td {
          padding: 12px 16px;
          text-align: left;
          border-bottom: 1px solid #f0f0f0;
        }

        th {
          background: #fafafa;
          font-weight: 600;
          color: #262626;
        }

        td {
          color: #595959;

          &:first-child {
            font-family: SFMono-Regular, Consolas, 'Liberation Mono', Menlo,
              Courier, monospace;
            color: #d4380d;
            background: #fff2e8;
            border-radius: 4px;
          }

          &:nth-child(2) {
            font-family: SFMono-Regular, <PERSON><PERSON><PERSON>, 'Liberation Mono', <PERSON><PERSON>,
              Courier, monospace;
            color: #1890ff;
          }

          &:nth-child(3) {
            font-family: <PERSON>Mono-<PERSON>, <PERSON><PERSON><PERSON>, 'Liberation Mono', <PERSON>lo,
              Courier, monospace;
            color: #52c41a;
          }
        }

        tr:hover {
          background: #fafafa;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .star-rating-demo {
    padding: 16px;

    .demo-container {
      .props-table {
        table {
          font-size: 12px;

          th,
          td {
            padding: 8px 12px;
          }
        }
      }
    }
  }
}
