/*
 * @description: 统一页面鉴权
 * @author: zp<PERSON>, AI Assistant
 * @Date: 2021-08-19 11:59:39
 * @LastEditTime: 2025-05-29 19:30:10
 */
import AuthHandler from '@/components/AuthHandler';
import NotFind from '@/pages/403';
import { isAuthenticated } from '@/utils/auth';
import { getQueryObj } from '@/utils/env';
import { Access, Outlet, useAccess } from '@umijs/max';

export default () => {
  const { isLogin } = useAccess();
  const { code } = getQueryObj();

  // 如果URL中有code参数或者已经认证，使用AuthHandler处理认证
  if (code || isAuthenticated()) {
    return (
      <AuthHandler>
        <Access accessible={isLogin} fallback={<NotFind />}>
          <Outlet />
        </Access>
      </AuthHandler>
    );
  }

  // 如果没有认证，显示未授权页面
  return <NotFind />;
};
