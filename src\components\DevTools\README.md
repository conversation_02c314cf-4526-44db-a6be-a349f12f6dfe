# DevTools 开发工具组件

开发工具组件，仅在开发模式下显示在侧边栏底部，提供快速访问开发和演示页面的入口。

## 功能特性

### 🔧 开发模式检测

- **环境检测**：仅在 `NODE_ENV === 'development'` 时显示
- **自动隐藏**：生产环境下自动隐藏，不影响生产版本

### 🎯 快速导航

- **星级评分演示**：快速访问星级评分组件的完整演示页面
- **家长端演示**：快速访问家长端问卷填写流程
- **工具提示**：每个链接都有详细的描述信息

### 🎨 视觉设计

- **现代化 UI**：渐变背景、悬停动画、发光效果
- **响应式设计**：支持侧边栏收起/展开状态
- **主题适配**：支持深色主题
- **环境标识**：显示"DEV"徽章，带有动画效果

## 组件结构

```
src/components/DevTools/
├── index.tsx              # 主组件文件
├── index.less             # 样式文件
└── README.md              # 组件文档
```

## 使用方式

### 在布局中使用

```tsx
import { DevTools } from '@/components';

export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  return {
    // 其他配置...
    menuFooterRender: () => <DevTools />,
  };
};
```

### 独立使用

```tsx
import { DevTools } from '@/components';

const MyComponent = () => {
  return (
    <div>
      {/* 其他内容 */}
      <DevTools />
    </div>
  );
};
```

## 配置选项

### 添加新的开发工具链接

在 `src/components/DevTools/index.tsx` 中的 `devLinks` 数组添加新项：

```tsx
const devLinks = [
  // 现有链接...
  {
    key: 'new-tool',
    icon: <ToolOutlined />,
    title: '新工具',
    path: '/new-tool',
    description: '新工具的描述',
  },
];
```

### 自定义样式

通过修改 `src/components/DevTools/index.less` 文件自定义样式：

```less
.dev-tools {
  // 自定义样式
  background: your-custom-color;
}
```

## 样式特性

### 交互效果

- **悬停动画**：鼠标悬停时的颜色和背景变化
- **点击反馈**：点击时的视觉反馈
- **平滑过渡**：所有状态变化都有平滑的过渡动画

### 响应式适配

- **侧边栏展开**：显示完整的图标和文字
- **侧边栏收起**：只显示图标，隐藏文字
- **移动端适配**：适配小屏幕设备

### 主题支持

- **浅色主题**：默认的浅色主题样式
- **深色主题**：通过 `prefers-color-scheme: dark` 自动适配
- **自定义主题**：支持通过 CSS 变量自定义主题色

## 环境变量

组件依赖以下环境变量：

- `NODE_ENV`：用于判断是否为开发环境
  - `development`：显示开发工具
  - `production`：隐藏开发工具

## 注意事项

1. **生产环境**：组件在生产环境下会自动隐藏，不会影响最终用户
2. **性能影响**：组件很轻量，对性能影响微乎其微
3. **路由依赖**：依赖 `@umijs/max` 的 `history` 对象进行路由跳转
4. **图标依赖**：使用 `@ant-design/icons` 的图标组件

## 扩展建议

### 添加更多开发工具

- API 文档链接
- 组件库文档
- 设计系统指南
- 测试页面入口
- 性能监控面板

### 增强功能

- 环境信息显示（版本号、构建时间等）
- 快速切换主题
- 开发者设置面板
- 调试信息显示

## 兼容性

- React 16.8+
- Ant Design 4.0+
- 现代浏览器（Chrome 60+, Firefox 55+, Safari 12+, Edge 79+）
