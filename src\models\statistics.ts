import {
  getSchoolList,
  getSchoolResponseTrend,
  getSchoolStatistics,
  getTeacherKeywords,
  getTeacherRanking,
  getTeacherScoreDistribution,
  getTeacherStatistics,
} from '@/services';
import { handleApiResponse, handleException } from '@/utils/errorHandler';
import { useCallback, useState } from 'react';

/**
 * 统计分析数据模型
 */
export default function useStatisticsModel() {
  // 加载状态
  const [loading, setLoading] = useState(false);
  const [chartLoading, setChartLoading] = useState(false);
  const [modalLoading, setModalLoading] = useState(false);

  // 学校列表
  const [schoolList, setSchoolList] = useState<API.ISSoSchoolInfo[]>([]);

  // 学校统计数据
  const [schoolStatistics, setSchoolStatistics] =
    useState<API.ISchoolStatistics | null>(null);

  // 趋势数据
  const [trendData, setTrendData] = useState<API.ITrendData[]>([]);

  // 教师排名数据
  const [teacherRanking, setTeacherRanking] = useState<API.ITeacherRanking[]>(
    [],
  );
  const [teacherTotal, setTeacherTotal] = useState(0);

  // 教师详情数据
  const [teacherDetail, setTeacherDetail] =
    useState<API.ITeacherStatistics | null>(null);
  const [scoreDistribution, setScoreDistribution] = useState<
    API.IScoreDistribution[]
  >([]);
  const [keywordData, setKeywordData] = useState<API.IKeywordData[]>([]);

  // 筛选条件
  const [filters, setFilters] = useState<API.IStatisticsQuery>({});

  // 获取学校列表
  const fetchSchoolList = useCallback(async () => {
    try {
      const response = await getSchoolList();
      const result = handleApiResponse(response);

      if (result.success) {
        setSchoolList(result.data || []);
        return result.data;
      } else {
        setSchoolList([]);
        return [];
      }
    } catch (error) {
      handleException(error, '获取学校列表失败');
      setSchoolList([]);
      return [];
    }
  }, []);

  // 获取学校统计数据
  const fetchSchoolStatistics = useCallback(
    async (params?: API.IStatisticsQuery) => {
      setLoading(true);
      try {
        const response = await getSchoolStatistics(params);
        const result = handleApiResponse(response);

        if (result.success) {
          setSchoolStatistics(result.data || null);
          return result.data;
        } else {
          setSchoolStatistics(null);
          return null;
        }
      } catch (error) {
        handleException(error, '获取学校统计数据失败');
        setSchoolStatistics(null);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取趋势数据
  const fetchTrendData = useCallback(
    async (schoolCode: string, params?: API.IStatisticsQuery) => {
      setChartLoading(true);
      try {
        const response = await getSchoolResponseTrend(schoolCode, params);
        const result = handleApiResponse(response);

        if (result.success) {
          setTrendData(result.data || []);
          return result.data;
        } else {
          setTrendData([]);
          return [];
        }
      } catch (error) {
        handleException(error, '获取趋势数据失败');
        setTrendData([]);
        return [];
      } finally {
        setChartLoading(false);
      }
    },
    [],
  );

  // 获取教师排名数据
  const fetchTeacherRanking = useCallback(
    async (params?: API.IStatisticsQuery) => {
      setLoading(true);
      try {
        const response = await getTeacherRanking(params);
        const result = handleApiResponse(response);

        if (result.success) {
          setTeacherRanking(result.data?.list || []);
          setTeacherTotal(result.data?.total || 0);
          return result.data;
        } else {
          setTeacherRanking([]);
          setTeacherTotal(0);
          return null;
        }
      } catch (error) {
        handleException(error, '获取教师排名失败');
        setTeacherRanking([]);
        setTeacherTotal(0);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取教师详情数据
  const fetchTeacherDetail = useCallback(
    async (teacherId: string, params?: API.IStatisticsQuery) => {
      setModalLoading(true);
      try {
        // 并行获取教师统计、评分分布、关键词数据
        const [statsResponse, distributionResponse, keywordsResponse] =
          await Promise.all([
            getTeacherStatistics({ ...params, sso_teacher_id: teacherId }),
            getTeacherScoreDistribution(teacherId, params),
            getTeacherKeywords(teacherId, params),
          ]);

        // 处理教师统计数据
        const statsResult = handleApiResponse(statsResponse);
        if (statsResult.success) {
          setTeacherDetail(statsResult.data || null);
        }

        // 处理评分分布数据
        const distributionResult = handleApiResponse(distributionResponse);
        if (distributionResult.success) {
          setScoreDistribution(distributionResult.data || []);
        }

        // 处理关键词数据
        const keywordsResult = handleApiResponse(keywordsResponse);
        if (keywordsResult.success) {
          setKeywordData(keywordsResult.data || []);
        }

        return {
          detail: statsResult.data,
          distribution: distributionResult.data,
          keywords: keywordsResult.data,
        };
      } catch (error) {
        handleException(error, '获取教师详情失败');
        setTeacherDetail(null);
        setScoreDistribution([]);
        setKeywordData([]);
        return null;
      } finally {
        setModalLoading(false);
      }
    },
    [],
  );

  // 更新筛选条件并刷新数据
  const updateFilters = useCallback(
    async (newFilters: API.IStatisticsQuery) => {
      setFilters(newFilters);

      // 刷新所有数据
      await Promise.all([
        fetchSchoolStatistics(newFilters),
        fetchTeacherRanking(newFilters),
        newFilters.sso_school_code &&
          fetchTrendData(newFilters.sso_school_code, newFilters),
      ]);
    },
    [fetchSchoolStatistics, fetchTeacherRanking, fetchTrendData],
  );

  // 清空教师详情数据
  const clearTeacherDetail = useCallback(() => {
    setTeacherDetail(null);
    setScoreDistribution([]);
    setKeywordData([]);
  }, []);

  return {
    // 状态
    loading,
    chartLoading,
    modalLoading,
    schoolList,
    schoolStatistics,
    trendData,
    teacherRanking,
    teacherTotal,
    teacherDetail,
    scoreDistribution,
    keywordData,
    filters,

    // 方法
    fetchSchoolList,
    fetchSchoolStatistics,
    fetchTrendData,
    fetchTeacherRanking,
    fetchTeacherDetail,
    updateFilters,
    clearTeacherDetail,
  };
}
