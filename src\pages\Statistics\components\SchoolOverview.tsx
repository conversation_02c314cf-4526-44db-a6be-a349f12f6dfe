import {
  PercentageOutlined,
  TeamOutlined,
  TrophyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Card, Col, Row, Skeleton, Statistic } from 'antd';
import React from 'react';

interface SchoolOverviewProps {
  data: API.ISchoolStatistics | null;
  loading?: boolean;
}

/**
 * 学校整体统计卡片组件
 */
const SchoolOverview: React.FC<SchoolOverviewProps> = ({
  data,
  loading = false,
}) => {
  if (loading) {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        {[1, 2, 3, 4].map((item) => (
          <Col span={6} key={item}>
            <Card>
              <Skeleton active paragraph={{ rows: 2 }} />
            </Card>
          </Col>
        ))}
      </Row>
    );
  }

  if (!data) {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card>
            <div
              style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}
            >
              暂无统计数据
            </div>
          </Card>
        </Col>
      </Row>
    );
  }

  return (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="学校平均分"
            value={data.school_average_score}
            precision={1}
            suffix="分"
            prefix={<TrophyOutlined style={{ color: '#faad14' }} />}
            valueStyle={{ color: '#faad14' }}
          />
          <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
            {data.sso_school_name || '全部学校'}
          </div>
        </Card>
      </Col>

      <Col span={6}>
        <Card>
          <Statistic
            title="参与人数"
            value={data.completed_responses}
            suffix="人"
            prefix={<UserOutlined style={{ color: '#52c41a' }} />}
            valueStyle={{ color: '#52c41a' }}
          />
          <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
            总响应数: {data.total_responses}
          </div>
        </Card>
      </Col>

      <Col span={6}>
        <Card>
          <Statistic
            title="完成率"
            value={data.completion_rate}
            precision={1}
            suffix="%"
            prefix={<PercentageOutlined style={{ color: '#1890ff' }} />}
            valueStyle={{ color: '#1890ff' }}
          />
          <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
            已完成 / 总数
          </div>
        </Card>
      </Col>

      <Col span={6}>
        <Card>
          <Statistic
            title="被评教师数"
            value={data.total_teachers_evaluated}
            suffix="人"
            prefix={<TeamOutlined style={{ color: '#722ed1' }} />}
            valueStyle={{ color: '#722ed1' }}
          />
          <div style={{ marginTop: 8, fontSize: '12px', color: '#999' }}>
            教师平均分: {data.teacher_average_score?.toFixed(1) || 0}分
          </div>
        </Card>
      </Col>
    </Row>
  );
};

export default SchoolOverview;
