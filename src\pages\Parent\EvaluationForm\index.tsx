import { StarRating } from '@/components';
import {
  ArrowLeftOutlined,
  CheckOutlined,
  SendOutlined,
  StarOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Alert,
  Button,
  Card,
  Input,
  Modal,
  Progress,
  Space,
  Tag,
  Typography,
} from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import './index.less';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

/**
 * 问卷填写页面
 */
const EvaluationForm: React.FC = () => {
  const {
    loading,
    selectedStudent,
    selectedQuestionnaire,
    teacherList,
    evaluationData,
    updateSchoolRating,
    updateTeacherRating,
    submitEvaluation,
    goBack,
  } = useModel('parent');

  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  // 本地状态管理输入值，避免中文输入法问题
  const [localSchoolComment, setLocalSchoolComment] = useState(
    evaluationData.school_comment,
  );
  const [localTeacherComments, setLocalTeacherComments] = useState<
    Record<string, string>
  >({});

  // 防抖定时器引用
  const schoolCommentTimeoutRef = useRef<NodeJS.Timeout>();
  const teacherCommentTimeoutRefs = useRef<Record<string, NodeJS.Timeout>>({});

  // 监听滚动事件，增强头部阴影效果
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      setIsScrolled(scrollTop > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // 同步 model 中的数据到本地状态
  useEffect(() => {
    setLocalSchoolComment(evaluationData.school_comment);
  }, [evaluationData.school_comment]);

  useEffect(() => {
    const comments: Record<string, string> = {};
    evaluationData.teacher_evaluations.forEach((evaluation) => {
      comments[evaluation.teacher_code] = evaluation.comment || '';
    });
    setLocalTeacherComments(comments);
  }, [evaluationData.teacher_evaluations]);

  // 计算评价进度和完成状态
  const getEvaluationStatus = () => {
    const availableTeachers = teacherList.filter((t) => !t.is_evaluated);

    // 暂时强制设置为不需要学校评价，直到问题解决
    // TODO: 需要确认问卷配置是否正确
    const includeSchoolEvaluation = false; // 临时修复
    // const includeSchoolEvaluation = selectedQuestionnaire?.include_school_evaluation ?? true;

    // 计算已完成评分的教师数量（评分 > 0）
    const completedTeacherCount = availableTeachers.filter((teacher) => {
      const evaluation = evaluationData.teacher_evaluations.find(
        (e) => e.teacher_code === teacher.code,
      );
      return evaluation && evaluation.rating > 0;
    }).length;

    // 检查学校评价是否完成
    const schoolEvaluationCompleted = includeSchoolEvaluation
      ? evaluationData.school_rating > 0
      : true; // 如果不需要学校评价，则认为已完成

    // 计算总项目数和已完成项目数
    const totalItems = availableTeachers.length + (includeSchoolEvaluation ? 1 : 0);
    const completedItems = completedTeacherCount + (includeSchoolEvaluation && evaluationData.school_rating > 0 ? 1 : 0);

    const progress = totalItems > 0
      ? Math.round((completedItems / totalItems) * 100)
      : 100;

    // 检查是否所有评价都已完成
    const allTeachersRated = completedTeacherCount === availableTeachers.length;
    const isAllCompleted = allTeachersRated && schoolEvaluationCompleted;

    // 调试信息
    console.log('进度计算调试信息:', {
      availableTeachersCount: availableTeachers.length,
      completedTeacherCount,
      includeSchoolEvaluation,
      originalIncludeSchoolEvaluation: selectedQuestionnaire?.include_school_evaluation,
      schoolRating: evaluationData.school_rating,
      schoolEvaluationCompleted,
      totalItems,
      completedItems,
      progress,
      allTeachersRated,
      isAllCompleted,
    });

    return {
      progress,
      isAllCompleted,
      totalItems,
      completedItems,
      allTeachersRated,
      schoolEvaluationCompleted,
    };
  };

  // 获取教师评分
  const getTeacherRating = (code: string) => {
    const evaluation = evaluationData.teacher_evaluations.find(
      (e) => e.teacher_code === code,
    );
    return evaluation?.rating || 0;
  };

  // 获取教师评价
  const getTeacherComment = (code: string) => {
    const evaluation = evaluationData.teacher_evaluations.find(
      (e) => e.teacher_code === code,
    );
    return evaluation?.comment || '';
  };

  // 处理学校评分
  const handleSchoolRating = (rating: number) => {
    updateSchoolRating(rating, evaluationData.school_comment);
  };

  // 处理学校评价
  const handleSchoolComment = (comment: string) => {
    updateSchoolRating(evaluationData.school_rating, comment);
  };

  // 处理教师评分
  const handleTeacherRating = (teacherId: string, rating: number) => {
    updateTeacherRating(teacherId, rating, getTeacherComment(teacherId));
  };

  // 处理教师评价
  const handleTeacherComment = (teacherId: string, comment: string) => {
    console.log('handleTeacherComment', teacherId, comment);
    updateTeacherRating(teacherId, getTeacherRating(teacherId), comment);
  };

  // 处理教师评价输入
  const handleTeacherCommentChange = (teacherCode: string) => {
    return (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      const value = e.target.value;
      setLocalTeacherComments((prev) => ({
        ...prev,
        [teacherCode]: value,
      }));

      // 清除之前的定时器
      if (teacherCommentTimeoutRefs.current[teacherCode]) {
        clearTimeout(teacherCommentTimeoutRefs.current[teacherCode]);
      }

      // 设置新的防抖定时器
      teacherCommentTimeoutRefs.current[teacherCode] = setTimeout(() => {
        handleTeacherComment(teacherCode, value);
      }, 300);
    };
  };

  // 处理学校评价输入
  const handleSchoolCommentChange = (
    e: React.ChangeEvent<HTMLTextAreaElement>,
  ) => {
    const value = e.target.value;
    setLocalSchoolComment(value);

    // 清除之前的定时器
    if (schoolCommentTimeoutRef.current) {
      clearTimeout(schoolCommentTimeoutRef.current);
    }

    // 设置新的防抖定时器
    schoolCommentTimeoutRef.current = setTimeout(() => {
      handleSchoolComment(value);
    }, 300);
  };

  // 提交评价
  const handleSubmit = async () => {
    const success = await submitEvaluation();
    if (success) {
      setShowSubmitModal(false);
    }
  };

  if (!selectedStudent || !selectedQuestionnaire) {
    return null;
  }

  const starMode = selectedQuestionnaire.star_mode || 5;
  const availableTeachers = teacherList.filter((t) => !t.is_evaluated);
  const isReadOnly = selectedQuestionnaire.is_submitted; // 已提交的问卷为只读模式
  const evaluationStatus = getEvaluationStatus();

  // 调试问卷信息
  console.log('问卷信息调试:', {
    questionnaireId: selectedQuestionnaire.questionnaire_id,
    title: selectedQuestionnaire.title,
    include_school_evaluation: selectedQuestionnaire.include_school_evaluation,
    isReadOnly,
    starMode,
  });

  return (
    <div className="evaluation-form-container">
      {/* 固定头部 */}
      <div className={`fixed-header ${isScrolled ? 'scrolled' : ''}`}>
        <div className="header-content">
          <div className="header-left">
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={goBack}
              className="back-button"
            >
              返回
            </Button>
          </div>

          <div className="header-center">
            <Title level={4} className="header-title">
              {selectedQuestionnaire.title}
              {isReadOnly && (
                <Tag color="success" style={{ marginLeft: 8 }}>
                  已提交
                </Tag>
              )}
            </Title>
            <div className="header-meta">
              <Text type="secondary">学生：{selectedStudent.name}</Text>
              <Text type="secondary">
                班级：{selectedQuestionnaire.class_info.name}
              </Text>
            </div>
          </div>

          <div className="header-right">
            <div className="progress-info">
              <Progress
                percent={evaluationStatus.progress}
                strokeColor="#1890ff"
                showInfo={false}
                size="small"
              />
              <Text type="secondary" className="progress-text">
                {evaluationStatus.progress}%
              </Text>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="evaluation-form-content">
        <Card className="form-card">
          {/* 只读模式提示 */}
          {isReadOnly && (
            <Alert
              message="问卷已提交"
              description="此问卷已提交，以下内容为只读模式，无法修改。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />
          )}

          {/* 学校评分区域 - 根据问卷设置决定是否显示 */}
          {selectedQuestionnaire.include_school_evaluation && (
            <div className="school-rating-section" style={{ backgroundColor: '#f0f8ff', border: '2px solid #1890ff', marginBottom: '24px' }}>
              <Card className="rating-card">
                <div className="rating-header">
                  <StarOutlined className="section-icon" />
                  <Title level={4}>学校整体评价</Title>
                  <Text type="secondary">（必填项）</Text>
                </div>

                <div className="rating-content">
                  <div className="rating-input">
                    <Text strong>请为学校整体表现评分：</Text>
                    <StarRating
                      mode={starMode as 5 | 10}
                      value={evaluationData.school_rating}
                      onChange={isReadOnly ? undefined : handleSchoolRating}
                      readOnly={isReadOnly}
                      size="large"
                      showValue={true}
                    />
                  </div>

                  <div className="comment-input">
                    <Text strong>评价建议（可选）：</Text>
                    <TextArea
                      placeholder="请输入您对学校的建议或评价..."
                      value={localSchoolComment}
                      onChange={
                        isReadOnly ? undefined : handleSchoolCommentChange
                      }
                      readOnly={isReadOnly}
                      rows={3}
                      maxLength={200}
                      showCount
                    />
                  </div>
                </div>
              </Card>
            </div>
          )}

          {/* 教师评价区域 */}
          <div className="teacher-rating-section">
            <div className="section-header">
              <UserOutlined className="section-icon" />
              <Title level={4}>教师评价</Title>
              <Text type="secondary">
                ({availableTeachers.length} 位教师待评价)
              </Text>
            </div>

            <Space direction="vertical" size="large" style={{ width: '100%' }}>
              {availableTeachers.map((teacher) => {
                const rating = getTeacherRating(teacher.code);
                const isRated = rating > 0;

                return (
                  <Card
                    key={teacher.code}
                    className={`teacher-card ${isRated ? 'rated' : ''}`}
                  >
                    <div className="teacher-info">
                      <div className="teacher-basic">
                        <Title level={5}>{teacher.name}</Title>
                        <Space>
                          <Tag color="blue">{teacher.courseName}</Tag>
                          {isRated && (
                            <Tag color="success" icon={<CheckOutlined />}>
                              已评价
                            </Tag>
                          )}
                        </Space>
                      </div>

                      <div className="teacher-rating">
                        <div className="rating-input">
                          <Text strong>评分：</Text>
                          <StarRating
                            mode={starMode as 5 | 10}
                            value={rating}
                            onChange={
                              isReadOnly
                                ? undefined
                                : (value) =>
                                    handleTeacherRating(teacher.code, value)
                            }
                            readOnly={isReadOnly}
                            size="default"
                            showValue={true}
                          />
                        </div>

                        <div className="comment-input">
                          <Text strong>评价（可选）：</Text>
                          <TextArea
                            placeholder={`请输入对${teacher.name}老师的评价...`}
                            value={localTeacherComments[teacher.code] || ''}
                            onChange={
                              isReadOnly
                                ? undefined
                                : handleTeacherCommentChange(teacher.code)
                            }
                            readOnly={isReadOnly}
                            rows={2}
                            maxLength={100}
                            showCount
                          />
                        </div>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </Space>
          </div>

          {/* 提交按钮 */}
          {!isReadOnly && (
            <div className="submit-section">
              <Button
                type="primary"
                size="large"
                icon={<SendOutlined />}
                onClick={() => setShowSubmitModal(true)}
                disabled={!evaluationStatus.isAllCompleted}
                loading={loading}
                className="submit-button"
              >
                提交评价
              </Button>

              {!evaluationStatus.isAllCompleted && (
                <Alert
                  message="请完成所有评分后再提交"
                  type="warning"
                  showIcon
                  style={{ marginTop: 16 }}
                />
              )}
            </div>
          )}
        </Card>
      </div>

      {/* 提交确认弹窗 */}
      <Modal
        title="确认提交评价"
        open={showSubmitModal}
        onOk={handleSubmit}
        onCancel={() => setShowSubmitModal(false)}
        confirmLoading={loading}
        okText="确认提交"
        cancelText="取消"
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message="提交后将无法修改"
            description="请确认您的评价内容无误后再提交"
            type="warning"
            showIcon
          />

          <div>
            {selectedQuestionnaire?.include_school_evaluation && (
              <Paragraph>
                <Text strong>学校评分：</Text>
                <StarRating
                  mode={starMode as 5 | 10}
                  value={evaluationData.school_rating}
                  readOnly={true}
                  size="default"
                  showValue={true}
                />
              </Paragraph>
            )}

            <Paragraph>
              <Text strong>教师评价：</Text>
              {evaluationData.teacher_evaluations.length} 位教师
            </Paragraph>
          </div>
        </Space>
      </Modal>
    </div>
  );
};

export default EvaluationForm;
