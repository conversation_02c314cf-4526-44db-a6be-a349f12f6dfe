import { message } from 'antd';

/**
 * 简化的错误处理工具类
 * 统一处理API响应和异常，自动显示消息
 */

/**
 * 统一处理API响应
 * @param response API响应
 * @param successMessage 成功消息（可选）
 * @param errorMessage 错误消息（可选，不传则使用后台返回的错误信息）
 */
export function handleApiResponse<T = any>(
  response: API.ResType<T>,
  successMessage?: string,
  errorMessage?: string,
): { success: boolean; data?: T; error?: string } {
  if (response.errCode === 0) {
    if (successMessage) {
      message.success(successMessage);
    }
    return { success: true, data: response.data };
  } else {
    const finalErrorMessage = response.msg || errorMessage || '操作失败';
    message.error(finalErrorMessage);
    return { success: false, error: finalErrorMessage };
  }
}

/**
 * 统一处理异常
 * @param error 异常对象
 * @param errorMessage 错误消息（可选，不传则使用异常信息）
 */
export function handleException(
  error: any,
  errorMessage?: string,
): { success: false; error: string } {
  const finalErrorMessage =
    errorMessage || error?.message || '网络异常，请稍后重试';
  message.error(finalErrorMessage);
  console.error('API调用异常:', error);
  return { success: false, error: finalErrorMessage };
}

// 简化的错误处理工具，只保留核心功能
