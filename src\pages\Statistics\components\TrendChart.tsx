import { Line } from '@ant-design/charts';
import { Card, Empty, Spin } from 'antd';
import React from 'react';

interface TrendChartProps {
  data: API.ITrendData[];
  loading?: boolean;
  title?: string;
}

/**
 * 月度评分趋势折线图组件
 */
const TrendChart: React.FC<TrendChartProps> = ({
  data,
  loading = false,
  title = '月度评分趋势',
}) => {
  // 处理图表数据
  const chartData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    const result: any[] = [];

    data.forEach((item) => {
      // 学校平均分
      if (item.avg_school_score !== undefined) {
        result.push({
          month: item.month,
          score: item.avg_school_score,
          type: '学校平均分',
        });
      }

      // 教师平均分
      if (item.avg_teacher_score !== undefined) {
        result.push({
          month: item.month,
          score: item.avg_teacher_score,
          type: '教师平均分',
        });
      }
    });

    return result.sort((a, b) => a.month.localeCompare(b.month));
  }, [data]);

  // 图表配置
  const config = {
    data: chartData,
    xField: 'month',
    yField: 'score',
    seriesField: 'type',
    smooth: true,
    animation: {
      appear: {
        animation: 'path-in',
        duration: 1000,
      },
    },
    color: ['#1890ff', '#52c41a'],
    point: {
      size: 4,
      shape: 'circle',
    },
    tooltip: {
      formatter: (datum: any) => {
        return {
          name: datum.type,
          value: `${datum.score?.toFixed(1) || 0}分`,
        };
      },
    },
    legend: {
      position: 'top' as const,
    },
    yAxis: {
      title: {
        text: '评分',
      },
      min: 0,
      max: 100,
    },
    xAxis: {
      title: {
        text: '月份',
      },
    },
    slider: {
      start: 0,
      end: 1,
    },
  };

  return (
    <Card title={title} style={{ marginBottom: 16 }}>
      <Spin spinning={loading}>
        {chartData.length === 0 ? (
          <Empty description="暂无趋势数据" style={{ padding: '40px 0' }} />
        ) : (
          <div style={{ height: 400 }}>
            <Line {...config} />
          </div>
        )}
      </Spin>
    </Card>
  );
};

export default TrendChart;
