import { useModel } from '@umijs/max';
import { Col, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import FilterForm from './components/FilterForm';
import SchoolOverview from './components/SchoolOverview';
import SubjectChart from './components/SubjectChart';
import TeacherDetailModal from './components/TeacherDetailModal';
import TeacherRanking from './components/TeacherRanking';
import TrendChart from './components/TrendChart';
import './index.less';

/**
 * 统计分析页面
 */
const Statistics: React.FC = () => {
  const {
    // 状态
    loading,
    chartLoading,
    modalLoading,
    schoolList,
    schoolStatistics,
    trendData,
    teacherRanking,
    teacherTotal,
    teacherDetail,
    scoreDistribution,
    keywordData,
    filters,

    // 方法
    fetchSchoolList,
    fetchSchoolStatistics,
    fetchTeacherRanking,
    fetchTeacherDetail,
    updateFilters,
    clearTeacherDetail,
  } = useModel('statistics');

  // 教师详情模态框状态
  const [modalVisible, setModalVisible] = useState(false);

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      // 获取学校列表
      await fetchSchoolList();

      // 获取初始统计数据
      await Promise.all([fetchSchoolStatistics(), fetchTeacherRanking()]);
    };

    initData();
  }, [fetchSchoolList, fetchSchoolStatistics, fetchTeacherRanking]);

  // 处理筛选
  const handleFilter = async (filterParams: API.IStatisticsQuery) => {
    await updateFilters(filterParams);
  };

  // 处理重置筛选
  const handleReset = async () => {
    await updateFilters({});
  };

  // 处理教师详情点击
  const handleTeacherClick = async (teacherId: string) => {
    setModalVisible(true);
    await fetchTeacherDetail(teacherId, filters);
  };

  // 关闭教师详情模态框
  const handleModalClose = () => {
    setModalVisible(false);
    clearTeacherDetail();
  };

  return (
    <div className="statistics-page">
      {/* 筛选表单 */}
      <FilterForm
        schoolList={schoolList}
        loading={loading}
        onFilter={handleFilter}
        onReset={handleReset}
      />

      {/* 学校整体统计 */}
      <SchoolOverview data={schoolStatistics} loading={loading} />

      {/* 图表区域 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <TrendChart
            data={trendData}
            loading={chartLoading}
            title="月度评分趋势"
          />
        </Col>
        <Col span={12}>
          <SubjectChart
            data={teacherRanking}
            loading={loading}
            title="学科平均分对比"
          />
        </Col>
      </Row>

      {/* 教师排行榜 */}
      <TeacherRanking
        data={teacherRanking}
        total={teacherTotal}
        loading={loading}
        onTeacherClick={handleTeacherClick}
      />

      {/* 教师详情模态框 */}
      <TeacherDetailModal
        visible={modalVisible}
        loading={modalLoading}
        teacherDetail={teacherDetail}
        scoreDistribution={scoreDistribution}
        keywordData={keywordData}
        onClose={handleModalClose}
      />
    </div>
  );
};

export default Statistics;
